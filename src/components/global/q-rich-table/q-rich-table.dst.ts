import { VNode, VNodeData } from "vue";

export type NodeType = VNode | string | number;
export type renderFunction = (text: any, record: any, index: number) => VNodeData | NodeType | NodeType[];
export type customRenderFunction = (column: any) => VNode;

export type IQRichTableColumn = Partial<{
  title: string ;
  key: string;
  dataIndex: string;
  customRender: renderFunction;
  customHeaderCell: customRenderFunction;
  scopedSlots: Record<string, string>;
  width: number | string;
  align: 'left' | 'right' | 'center';
  fixed: boolean | string;
  sorter: boolean;
  minWidth?: number;
  // [key: string]: any;
}>;

export interface IQRichTablePagination {
  size?: string;
  total: number;
  current: number;
  pageSize: number;
  defaultCurrent?: number;
  defaultPageSize?: number;
  showQuickJumper?: boolean | object;
  showSizeChanger?: boolean;
  pageSizeOptions?: string[];
  onChange?(current: number, pageSize: number): void;
  onShowSizeChange?(current: number, pageSize: number): void;
}

export interface IQRichTableScroll {
  x: number | boolean;
  y: number;
  scrollToFirstRowOnChange: boolean;
}
